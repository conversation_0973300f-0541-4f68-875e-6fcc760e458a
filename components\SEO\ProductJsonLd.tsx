"use client";

import { ProductData } from "@/types";
import Script from "next/script";
import { siteConfig } from "@/config/site";

interface ProductJsonLdProps {
  product: ProductData;
  url: string;
}

/**
 * Generates JSON-LD structured data for a product
 * This helps search engines understand the product information
 * and can enhance search results with rich snippets
 */
const ProductJsonLd = ({ product, url }: ProductJsonLdProps) => {
  // Calculate the discounted price if available
  const discountedPrice =
    product.discount && product.discount !== "0"
      ? Math.round(
          product.price - product.price * (parseInt(product.discount) / 100)
        )
      : null;

  // Format the product data for JSON-LD
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.title || product.name,
    description:
      product.description ||
      `Premium quality ${product.category} furniture from ${siteConfig.name}`,
    image:
      product.images && product.images.length > 0
        ? product.images.map((img: string) =>
            img.startsWith("http") ? img : `${url}${img}`
          )
        : product.image
        ? [
            product.image.startsWith("http")
              ? product.image
              : `${url}${product.image}`,
          ]
        : [`${url}/og-image.jpg`],
    url: `${url}/products/${product.id}`,
    sku: product.id,
    brand: {
      "@type": "Brand",
      name: siteConfig.name,
    },
    category: product.category,
    offers: {
      "@type": "Offer",
      price: discountedPrice || product.price,
      priceCurrency: "USD",
      availability: product.available
        ? "https://schema.org/InStock"
        : "https://schema.org/OutOfStock",
      url: `${url}/products/${product.id}`,
      priceValidUntil: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      )
        .toISOString()
        .split("T")[0],
    },
  };

  return (
    <Script id={`product-jsonld-${product.id}`} type="application/ld+json">
      {JSON.stringify(jsonLd)}
    </Script>
  );
};

export default ProductJsonLd;
