/**
 * Site configuration file
 * 
 * This file contains all the site-wide configuration settings.
 * Update these values to match your company information.
 */

export const siteConfig = {
  // Company Information
  name: "Chinioti Wooden Art",
  shortName: "Chinioti Art",
  description: "Pakistan's premier destination for authentic Chinioti wooden furniture, handcrafted with traditional craftsmanship and modern designs.",
  slogan: "Traditional Craftsmanship, Modern Designs",
  
  // Contact Information
  contact: {
    email: "<EMAIL>",
    phone: "+923421401866",
    whatsapp: "+923421401866",
    address: {
      street: "Chiniot Furniture Market",
      city: "Chiniot",
      region: "Punjab",
      postalCode: "35400",
      country: "Pakistan",
    },
  },
  
  // Social Media
  social: {
    facebook: "https://www.facebook.com/chiniotiart",
    instagram: "https://www.instagram.com/chiniotiart",
    twitter: "https://twitter.com/chiniotiart",
    youtube: "https://www.youtube.com/chiniotiart",
  },
  
  // SEO Configuration
  seo: {
    // Domain name (without https:// or trailing slash)
    domain: "chiniotiwoodenart.com",
    
    // Default title template
    titleTemplate: "%s | Chinioti Wooden Art",
    
    // Default meta tags
    keywords: [
      "Chinioti furniture",
      "wooden furniture",
      "Pakistan furniture",
      "handcrafted furniture",
      "premium furniture",
      "wooden beds",
      "wooden tables",
      "wooden chairs",
    ],
    
    // Location coordinates for maps
    location: {
      latitude: 31.7094062,
      longitude: 72.9703328,
    },
    
    // Business hours
    businessHours: {
      weekdays: {
        open: "09:00",
        close: "18:00",
      },
      saturday: {
        open: "09:00",
        close: "18:00",
      },
      sunday: {
        open: "10:00",
        close: "16:00",
      },
    },
  },
  
  // Theme Configuration
  theme: {
    // Primary colors
    colors: {
      primary: "#8B4513", // Brown
      secondary: "#D2B48C", // Tan
      accent: "#A0522D", // Sienna
      background: "#F8F8F8", // Off-white
      text: "#333333", // Dark gray
    },
    
    // Font families
    fonts: {
      primary: "Poppins",
      secondary: "serif",
    },
  },
};

// Helper function to get the full URL
export const getUrl = (path: string = "") => {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${siteConfig.seo.domain}`;
  return `${baseUrl}${path.startsWith("/") ? path : `/${path}`}`;
};

export default siteConfig;
